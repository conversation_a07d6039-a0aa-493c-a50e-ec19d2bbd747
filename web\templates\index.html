{{template "base.html" .}}

{{define "content"}}
<!-- 快速操作工具栏 -->
<div style="display: flex; justify-content: center; margin-bottom: 2rem;">
    <div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-lg border border-gray-100/50 p-6" style="max-width: 1200px; width: 100%;">
    <div class="flex flex-wrap items-center justify-between gap-6">
        <div class="flex items-center space-x-6">
            <h3 class="text-xl font-bold text-gray-900 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">快速操作</h3>
            <div class="flex space-x-3">
                <button id="templateManagementBtn" onclick="openTaskTemplateManager()" class="px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 rounded-xl text-sm font-medium hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 border border-blue-200/50 shadow-sm" style="display: none;">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    模板管理
                </button>
                <button onclick="generateAnalyticsReport()" class="px-4 py-2 bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 rounded-xl text-sm font-medium hover:from-purple-100 hover:to-pink-100 transition-all duration-300 border border-purple-200/50 shadow-sm">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    分析
                </button>
                <button onclick="createBackup()" class="px-4 py-2 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 rounded-xl text-sm font-medium hover:from-green-100 hover:to-emerald-100 transition-all duration-300 border border-green-200/50 shadow-sm">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                    </svg>
                    备份
                </button>
                <button onclick="showActivityFeed()" class="px-4 py-2 bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-700 rounded-xl text-sm font-medium hover:from-yellow-100 hover:to-orange-100 transition-all duration-300 border border-yellow-200/50 shadow-sm">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    活动
                </button>
                <button id="typeManagementBtn" onclick="openTaskTypeManager()" class="hidden px-4 py-2 bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 rounded-xl text-sm font-medium hover:from-orange-100 hover:to-red-100 transition-all duration-300 border border-orange-200/50 shadow-sm">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    类型管理
                </button>
                <button id="versionManagementBtn" onclick="openTaskVersionManager()" class="hidden px-4 py-2 bg-gradient-to-r from-purple-50 to-indigo-50 text-purple-700 rounded-xl text-sm font-medium hover:from-purple-100 hover:to-indigo-100 transition-all duration-300 border border-purple-200/50 shadow-sm">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    版本管理
                </button>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-600 font-medium">
                <span id="lastSyncTime">刚刚同步</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse shadow-sm" title="实时同步"></div>
                <span class="text-xs text-green-600 font-medium">在线</span>
            </div>
        </div>
    </div>
</div>
</div>



<div style="width: 100%; padding: 0; margin: 0; display: flex; justify-content: center;">
    <div class="grid-responsive" style="max-width: 1200px; width: 100%; margin: 0; padding: 0; gap: 0.75rem; display: grid; grid-template-columns: repeat(3, 1fr);">
    <!-- 待办列 -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div class="column-header todo text-white rounded-t-2xl">
            <h2 class="text-lg font-semibold flex items-center">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                待办
                <span class="ml-3 bg-white bg-opacity-25 text-xs px-3 py-1 rounded-full font-medium">
                    {{len (index .tasksByStatus .statusTodo)}}
                </span>
            </h2>
        </div>
        <div class="p-4">
            <div id="todo-column" class="space-y-2 min-h-[200px]" data-status="todo">
                {{$todoTasks := index .tasksByStatus .statusTodo}}
                {{range $index, $task := $todoTasks}}
                <div class="task-card animate-fade-in priority-{{$task.Priority}} {{if ge $index 5}}task-card-hidden hidden{{end}}" data-task-id="{{$task.ID}}"
                     data-title="{{$task.Title}}" data-description="{{$task.Description}}" data-priority="{{$task.Priority}}"
                     data-type="{{$task.Type}}" data-status="{{$task.Status}}" data-created="{{$task.CreatedAt.Format "2006-01-02 15:04:05"}}"
                     data-updated="{{$task.UpdatedAt.Format "2006-01-02 15:04:05"}}"
                     {{if $task.AssignedTo}}data-assigned-to="{{$task.AssignedTo}}"{{end}}
                     {{if $task.CreatedBy}}data-created-by="{{$task.CreatedBy}}"{{end}}
                     {{if $task.StartDate}}data-start-date="{{$task.StartDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.EndDate}}data-end-date="{{$task.EndDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.DueDate}}data-due-date="{{$task.DueDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     data-estimated-hours="{{$task.EstimatedHours}}" data-actual-hours="{{$task.ActualHours}}"
                     onclick="openTaskDetailModal('{{$task.ID}}', event)"
                     style="cursor: pointer;">
                    <div class="flex justify-between items-start mb-1">
                        <div class="flex items-start flex-1">
                            <input type="checkbox" class="task-checkbox hidden mr-1 mt-1" data-task-id="{{$task.ID}}">
                            <div class="flex items-center space-x-1.5 flex-1">
                                <span class="task-type-icon text-xs" title="{{$task.Type.GetDisplayName}}">{{$task.Type.GetTypeIcon}}</span>
                                <h3 class="task-title flex-1 text-sm font-medium" data-task-id="{{$task.ID}}">{{$task.Title}}</h3>
                                <span class="task-type-name text-xs text-gray-500 bg-gray-50 px-1 py-0.5 rounded font-medium">{{$task.Type.GetDisplayName}}</span>
                            </div>
                        </div>
                        <div class="task-actions ml-1">
                            <button class="task-action-btn edit-task" data-task-id="{{$task.ID}}" title="编辑任务" onclick="event.stopPropagation(); editTaskFromButton('{{$task.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="task-action-btn delete-task delete" data-task-id="{{$task.ID}}" title="删除任务" onclick="event.stopPropagation(); deleteTaskFromButton('{{$task.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {{if $task.Description}}
                    <p class="task-description text-xs text-gray-600 my-1">{{stripHTML $task.Description}}</p>
                    {{end}}
                    {{if or $task.StartDate $task.EndDate}}
                    <div class="task-meta text-xs mb-1">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>{{$task.FormatDateRange}}</span>
                        {{if and $task.StartDate $task.EndDate}}
                        <div class="time-progress-indicator" data-start="{{$task.StartDate.Format "2006-01-02"}}" data-end="{{$task.EndDate.Format "2006-01-02"}}">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text"></span>
                        </div>
                        {{end}}
                    </div>
                    {{end}}
                    <!-- 底部信息区域 -->
                    <div class="mt-1.5 pt-1.5 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <!-- 左侧：优先级和用户信息 -->
                            <div class="flex items-center space-x-1.5">
                                <span class="priority-badge {{$task.Priority}} text-xs px-1.5 py-0.5">
                                    {{$task.Priority.GetDisplayName}}
                                </span>
                                {{if $task.AssignedUser}}
                                <div class="flex items-center space-x-1" title="分配给: {{$task.AssignedUser.FullName}}">
                                    <svg class="w-2.5 h-2.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-xs text-blue-600 font-medium">{{$task.AssignedUser.FullName}}</span>
                                </div>
                                {{end}}
                            </div>

                            <!-- 右侧：创建信息 -->
                            <div class="flex items-center space-x-1 text-xs text-gray-500">
                                {{if $task.Creator}}
                                <span title="创建者: {{$task.Creator.FullName}}">{{$task.Creator.FullName}}</span>
                                {{else if $task.CreatedBy}}
                                <span title="创建者ID: {{$task.CreatedBy}}">创建者</span>
                                {{else}}
                                <span>系统</span>
                                {{end}}
                                <span>•</span>
                                <span>{{$task.CreatedAt.Format "01-02"}}</span>
                            </div>
                        </div>
                    </div>
                </div>
                {{end}}
                {{if gt (len $todoTasks) 5}}
                <button class="show-more-tasks-btn w-full mt-3 p-2 border border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-400 hover:text-blue-600 transition-colors duration-200 flex items-center justify-center text-sm" data-column="todo">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <span class="show-more-text">查看更多历史任务</span>
                    <span class="show-less-text hidden">收起历史任务</span>
                </button>
                {{end}}
            </div>
        </div>
    </div>

    <!-- 进行中列 -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div class="column-header in-progress text-white rounded-t-2xl">
            <h2 class="text-lg font-semibold flex items-center">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                进行中
                <span class="ml-3 bg-white bg-opacity-25 text-xs px-3 py-1 rounded-full font-medium">
                    {{len (index .tasksByStatus .statusInProgress)}}
                </span>
            </h2>
        </div>
        <div class="p-4">
            <div id="inprogress-column" class="space-y-2 min-h-[200px]" data-status="in_progress">
                {{$inProgressTasks := index .tasksByStatus .statusInProgress}}
                {{range $index, $task := $inProgressTasks}}
                <div class="task-card animate-fade-in priority-{{$task.Priority}} {{if ge $index 5}}task-card-hidden hidden{{end}}" data-task-id="{{$task.ID}}"
                     data-title="{{$task.Title}}" data-description="{{$task.Description}}" data-priority="{{$task.Priority}}"
                     data-type="{{$task.Type}}" data-status="{{$task.Status}}" data-created="{{$task.CreatedAt.Format "2006-01-02 15:04:05"}}"
                     data-updated="{{$task.UpdatedAt.Format "2006-01-02 15:04:05"}}"
                     {{if $task.AssignedTo}}data-assigned-to="{{$task.AssignedTo}}"{{end}}
                     {{if $task.CreatedBy}}data-created-by="{{$task.CreatedBy}}"{{end}}
                     {{if $task.StartDate}}data-start-date="{{$task.StartDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.EndDate}}data-end-date="{{$task.EndDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.DueDate}}data-due-date="{{$task.DueDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     data-estimated-hours="{{$task.EstimatedHours}}" data-actual-hours="{{$task.ActualHours}}"
                     onclick="openTaskDetailModal('{{$task.ID}}', event)"
                     style="cursor: pointer;">
                    <div class="flex justify-between items-start mb-1">
                        <div class="flex items-start flex-1">
                            <input type="checkbox" class="task-checkbox hidden mr-1 mt-1" data-task-id="{{.ID}}">
                            <div class="flex items-center space-x-1.5 flex-1">
                                <span class="task-type-icon text-xs" title="{{.Type.GetDisplayName}}">{{.Type.GetTypeIcon}}</span>
                                <h3 class="task-title flex-1 text-sm font-medium" data-task-id="{{.ID}}">{{.Title}}</h3>
                                <span class="task-type-name text-xs text-gray-500 bg-gray-50 px-1 py-0.5 rounded font-medium">{{.Type.GetDisplayName}}</span>
                            </div>
                        </div>
                        <div class="task-actions ml-1">
                            <button class="task-action-btn edit-task" data-task-id="{{.ID}}" title="编辑任务" onclick="event.stopPropagation(); editTaskFromButton('{{.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="task-action-btn delete-task delete" data-task-id="{{.ID}}" title="删除任务" onclick="event.stopPropagation(); deleteTaskFromButton('{{.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {{if .Description}}
                    <p class="task-description text-xs text-gray-600 my-1">{{stripHTML .Description}}</p>
                    {{end}}
                    {{if or .StartDate .EndDate}}
                    <div class="task-meta text-xs mb-1">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>{{.FormatDateRange}}</span>
                        {{if and .StartDate .EndDate}}
                        <div class="time-progress-indicator" data-start="{{.StartDate.Format "2006-01-02"}}" data-end="{{.EndDate.Format "2006-01-02"}}">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text"></span>
                        </div>
                        {{end}}
                    </div>
                    {{end}}
                    <!-- 底部信息区域 -->
                    <div class="mt-1.5 pt-1.5 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <!-- 左侧：优先级和用户信息 -->
                            <div class="flex items-center space-x-1.5">
                                <span class="priority-badge {{.Priority}} text-xs px-1.5 py-0.5">
                                    {{.Priority.GetDisplayName}}
                                </span>
                                {{if .AssignedUser}}
                                <div class="flex items-center space-x-1" title="分配给: {{.AssignedUser.FullName}}">
                                    <svg class="w-2.5 h-2.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-xs text-blue-600 font-medium">{{.AssignedUser.FullName}}</span>
                                </div>
                                {{end}}
                            </div>

                            <!-- 右侧：创建信息 -->
                            <div class="flex items-center space-x-1 text-xs text-gray-500">
                                {{if .Creator}}
                                <span title="创建者: {{.Creator.FullName}}">{{.Creator.FullName}}</span>
                                {{else if .CreatedBy}}
                                <span title="创建者ID: {{.CreatedBy}}">创建者</span>
                                {{else}}
                                <span>系统</span>
                                {{end}}
                                <span>•</span>
                                <span>{{.CreatedAt.Format "01-02"}}</span>
                            </div>
                        </div>
                    </div>
                </div>
                {{end}}
                {{if gt (len $inProgressTasks) 5}}
                <button class="show-more-tasks-btn w-full mt-3 p-2 border border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-yellow-400 hover:text-yellow-600 transition-colors duration-200 flex items-center justify-center text-sm" data-column="in_progress">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <span class="show-more-text">查看更多历史任务</span>
                    <span class="show-less-text hidden">收起历史任务</span>
                </button>
                {{end}}
            </div>
        </div>
    </div>

    <!-- 已完成列 -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div class="column-header done text-white rounded-t-2xl">
            <h2 class="text-lg font-semibold flex items-center">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                已完成
                <span class="ml-3 bg-white bg-opacity-25 text-xs px-3 py-1 rounded-full font-medium">
                    {{len (index .tasksByStatus .statusDone)}}
                </span>
            </h2>
        </div>
        <div class="p-4">
            <div id="done-column" class="space-y-2 min-h-[200px]" data-status="done">
                {{$doneTasks := index .tasksByStatus .statusDone}}
                {{range $index, $task := $doneTasks}}
                <div class="task-card animate-fade-in priority-{{$task.Priority}} opacity-75 {{if ge $index 5}}task-card-hidden hidden{{end}}" data-task-id="{{$task.ID}}"
                     data-title="{{$task.Title}}" data-description="{{$task.Description}}" data-priority="{{$task.Priority}}"
                     data-type="{{$task.Type}}" data-status="{{$task.Status}}" data-created="{{$task.CreatedAt.Format "2006-01-02 15:04:05"}}"
                     data-updated="{{$task.UpdatedAt.Format "2006-01-02 15:04:05"}}"
                     {{if $task.AssignedTo}}data-assigned-to="{{$task.AssignedTo}}"{{end}}
                     {{if $task.CreatedBy}}data-created-by="{{$task.CreatedBy}}"{{end}}
                     {{if $task.StartDate}}data-start-date="{{$task.StartDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.EndDate}}data-end-date="{{$task.EndDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     {{if $task.DueDate}}data-due-date="{{$task.DueDate.Format "2006-01-02 15:04:05"}}"{{end}}
                     data-estimated-hours="{{$task.EstimatedHours}}" data-actual-hours="{{$task.ActualHours}}"
                     onclick="openTaskDetailModal('{{$task.ID}}', event)"
                     style="cursor: pointer;">
                    <div class="flex justify-between items-start mb-1">
                        <div class="flex items-start flex-1">
                            <input type="checkbox" class="task-checkbox hidden mr-1 mt-1" data-task-id="{{$task.ID}}">
                            <div class="flex items-center space-x-1.5 flex-1">
                                <span class="task-type-icon text-xs opacity-75" title="{{$task.Type.GetDisplayName}}">{{$task.Type.GetTypeIcon}}</span>
                                <h3 class="task-title flex-1 text-sm font-medium line-through" data-task-id="{{$task.ID}}">{{$task.Title}}</h3>
                                <span class="task-type-name text-xs text-gray-500 bg-gray-50 px-1 py-0.5 rounded font-medium opacity-75">{{$task.Type.GetDisplayName}}</span>
                            </div>
                        </div>
                        <div class="task-actions ml-1">
                            <button class="task-action-btn edit-task" data-task-id="{{$task.ID}}" title="编辑任务" onclick="event.stopPropagation(); editTaskFromButton('{{$task.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="task-action-btn delete-task delete" data-task-id="{{$task.ID}}" title="删除任务" onclick="event.stopPropagation(); deleteTaskFromButton('{{$task.ID}}');">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {{if $task.Description}}
                    <p class="task-description text-xs text-gray-600 my-1 opacity-75 line-through">{{stripHTML $task.Description}}</p>
                    {{end}}
                    {{if or $task.StartDate $task.EndDate}}
                    <div class="task-meta text-xs mb-1 opacity-75">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span>{{$task.FormatDateRange}}</span>
                        <!-- 已完成任务不显示进度条 -->
                    </div>
                    {{end}}
                    <!-- 底部信息区域 -->
                    <div class="mt-1.5 pt-1.5 border-t border-gray-100 opacity-75">
                        <div class="flex items-center justify-between">
                            <!-- 左侧：优先级和用户信息 -->
                            <div class="flex items-center space-x-1.5">
                                <span class="priority-badge {{$task.Priority}} text-xs px-1.5 py-0.5">
                                    {{$task.Priority.GetDisplayName}}
                                </span>
                                {{if $task.AssignedUser}}
                                <div class="flex items-center space-x-1" title="分配给: {{$task.AssignedUser.FullName}}">
                                    <svg class="w-2.5 h-2.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-xs text-blue-600 font-medium">{{$task.AssignedUser.FullName}}</span>
                                </div>
                                {{end}}
                            </div>

                            <!-- 右侧：创建信息 -->
                            <div class="flex items-center space-x-1 text-xs text-gray-500">
                                {{if $task.Creator}}
                                <span title="创建者: {{$task.Creator.FullName}}">{{$task.Creator.FullName}}</span>
                                {{else if $task.CreatedBy}}
                                <span title="创建者ID: {{$task.CreatedBy}}">创建者</span>
                                {{else}}
                                <span>系统</span>
                                {{end}}
                                <span>•</span>
                                <span>{{$task.UpdatedAt.Format "01-02"}}</span>
                            </div>
                        </div>
                    </div>
                </div>
                {{end}}
                {{if gt (len $doneTasks) 5}}
                <button class="show-more-tasks-btn w-full mt-3 p-2 border border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-green-400 hover:text-green-600 transition-colors duration-200 flex items-center justify-center text-sm" data-column="done">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <span class="show-more-text">查看更多历史任务</span>
                    <span class="show-less-text hidden">收起历史任务</span>
                </button>
                {{end}}
            </div>
        </div>
    </div>
    </div>
</div>



{{end}}
