<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-4">模态框调试</h1>
        
        <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            测试打开模态框
        </button>
        
        <div class="mt-4">
            <h2 class="text-lg font-semibold mb-2">控制台输出:</h2>
            <div id="console" class="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto"></div>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div id="taskDetailModal" class="fixed inset-0 bg-gray-900 bg-opacity-60 hidden z-[9999] flex items-center justify-center p-3 backdrop-blur-sm">
        <div class="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[96vh] overflow-hidden relative z-[10000] border border-gray-200">
            <!-- 优化的头部区域 - 融合关键信息 -->
            <div class="bg-gradient-to-r from-indigo-500 via-violet-500 to-purple-500 border-b border-indigo-200 px-4 py-3">
                <div class="flex items-center justify-between">
                    <!-- 左侧：标题和基本信息 -->
                    <div class="flex items-center space-x-2.5">
                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center shadow-sm backdrop-blur-sm">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-base font-semibold text-white mb-0.5" id="detailModalTitle">任务详情</h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-white text-opacity-80" id="detailTaskId">ID: #</span>
                                <span id="detailStatusBadge" class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium border border-white border-opacity-30 bg-white bg-opacity-20 text-white"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 中间：关键信息条 -->
                    <div class="hidden lg:flex items-center space-x-3">
                        <!-- 时间跟踪信息 -->
                        <div class="flex items-center space-x-1.5 bg-white bg-opacity-15 backdrop-blur-sm rounded-lg px-2.5 py-1.5 border border-white border-opacity-20">
                            <svg class="w-3.5 h-3.5 text-white text-opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-xs text-white">
                                <div class="font-medium" id="headerTimeRange">时间安排</div>
                                <div class="text-white text-opacity-80" id="headerTimeProgress">进度跟踪</div>
                            </div>
                        </div>
                        
                        <!-- 进度状态 -->
                        <div class="flex items-center space-x-1.5 bg-white bg-opacity-15 backdrop-blur-sm rounded-lg px-2.5 py-1.5 border border-white border-opacity-20">
                            <svg class="w-3.5 h-3.5 text-white text-opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <div class="text-xs text-white">
                                <div class="font-medium" id="headerProgressPercent">0%</div>
                                <div class="text-white text-opacity-80">完成度</div>
                            </div>
                        </div>
                        
                        <!-- 优先级 -->
                        <div class="flex items-center space-x-1.5 bg-white bg-opacity-15 backdrop-blur-sm rounded-lg px-2.5 py-1.5 border border-white border-opacity-20">
                            <svg class="w-3.5 h-3.5 text-white text-opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <div class="text-xs text-white">
                                <div class="font-medium" id="headerPriorityText">优先级</div>
                                <div class="text-white text-opacity-80" id="headerPriorityLevel">中等</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：关闭按钮 -->
                    <div class="flex items-center space-x-2">
                        <button id="closeDetailModal" class="p-1.5 text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-20 rounded transition-all duration-200">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-4">
                <h2 id="detailTitle" class="text-lg font-bold text-gray-900 mb-2">测试任务标题</h2>
                <div id="detailDescription" class="text-sm text-gray-700">测试任务描述</div>
            </div>
        </div>
    </div>

    <script>
        // 重定向console.log到页面
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-red-400' : 'text-green-400';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // 测试函数
        function testModal() {
            console.log('开始测试模态框');
            
            const modal = document.getElementById('taskDetailModal');
            if (!modal) {
                console.error('找不到模态框元素');
                return;
            }
            
            console.log('找到模态框元素');
            
            // 模拟任务数据
            const testTask = {
                id: 'test-123',
                title: '测试任务标题',
                description: '这是一个测试任务的描述',
                status: 'in-progress',
                priority: 'high',
                start_date: '2024-01-01T09:00:00Z',
                end_date: '2024-01-15T18:00:00Z'
            };
            
            console.log('测试任务数据:', testTask);
            
            // 显示模态框
            modal.classList.remove('hidden');
            console.log('模态框已显示');
            
            // 填充基本信息
            const titleElement = document.getElementById('detailModalTitle');
            const taskIdElement = document.getElementById('detailTaskId');
            
            if (titleElement) {
                titleElement.textContent = testTask.title;
                console.log('标题已设置');
            } else {
                console.error('找不到标题元素');
            }
            
            if (taskIdElement) {
                taskIdElement.textContent = `ID: #${testTask.id.slice(-8)}`;
                console.log('ID已设置');
            } else {
                console.error('找不到ID元素');
            }
            
            // 测试标题栏信息
            const timeRangeElement = document.getElementById('headerTimeRange');
            const progressElement = document.getElementById('headerProgressPercent');
            const priorityElement = document.getElementById('headerPriorityText');
            
            if (timeRangeElement) {
                timeRangeElement.textContent = '1月1日 - 1月15日';
                console.log('时间范围已设置');
            } else {
                console.error('找不到时间范围元素');
            }
            
            if (progressElement) {
                progressElement.textContent = '65%';
                console.log('进度已设置');
            } else {
                console.error('找不到进度元素');
            }
            
            if (priorityElement) {
                priorityElement.textContent = '🔴';
                console.log('优先级已设置');
            } else {
                console.error('找不到优先级元素');
            }
            
            console.log('测试完成');
        }
        
        // 绑定测试按钮
        document.getElementById('testBtn').addEventListener('click', testModal);
        
        // 绑定关闭按钮
        document.getElementById('closeDetailModal').addEventListener('click', function() {
            console.log('关闭模态框');
            document.getElementById('taskDetailModal').classList.add('hidden');
        });
        
        console.log('调试页面已加载');
    </script>
</body>
</html>
